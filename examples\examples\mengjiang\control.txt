# EF5 蒙江流域水文模型配置文件
# 支持多模型、多路由方法和DREAM自动校准
# 配置时间：3小时模拟运行 (2009-01-01 08:00 到 2009-01-01 11:00)

[Basic]
# 基础地理数据文件
DEM=basic\dem.asc
DDM=basic\ddm.asc
FAM=basic\fac.asc
PROJ=geographic
ESRIDDM=true
SelfFAM=true

# 降水强迫数据配置
[PrecipForcing RAIN_3H]
TYPE=ASC
UNIT=mm/3h
FREQ=3h
LOC=precip\
NAME=rain_YYYYMMDDHH.asc

# PET强迫数据配置
[PETForcing PET_MONTHLY]
TYPE=ASC
UNIT=mm/d
FREQ=m
LOC=pet\
NAME=PET_MM.asc

# 太平站点配置
[Gauge Taiping]
LON=110.5
LAT=24.0
OBS=obs\taiping.csv
BASINAREA=1000.0
OUTPUTTS=TRUE

# 蒙江流域配置
[Basin Mengjiang]
GAUGE=Taiping

# ========================================
# 水文模型参数配置
# ========================================

# HP模型参数配置
[HPParamSet Mengjiang_HP]
GAUGE=Taiping
PRECIP=1.0
SPLIT=0.5

# CREST模型参数配置
[CRESTParamSet Mengjiang_CREST]
GAUGE=Taiping
WM=150.0
B=2.0
IM=0.1
KE=1.0
FC=10.0
IWU=50.0

# CRESTPHYS模型参数配置 (修复NaN问题)
[CRESTPhysParamSet Mengjiang_CRESTPHYS]
GAUGE=Taiping
WM=200.0
B=1.5
IM=0.2
KE=0.8
FC=15.0
KSOIL=0.2
IWU=60.0
IGW=30.0
HMAXAQ=2.0
GWC=1.2
GWE=1.1

# HYMOD模型参数配置
[HYMODParamSet Mengjiang_HYMOD]
GAUGE=Taiping
HUZ=100.0
B=1.5
ALP=0.8
NQ=3
KQ=0.5
KS=0.1
XCUZ=50.0
XQ=0.0
XS=0.0
PRECIP=1.0

# SAC-SMA模型参数配置
[SACParamSet Mengjiang_SAC]
GAUGE=Taiping
UZTWM=75.0
UZFWM=25.0
UZK=0.3
PCTIM=0.1
ADIMP=0.1
RIVA=0.0
ZPERC=50.0
REXP=2.0
LZTWM=100.0
LZFSM=60.0
LZFPM=100.0
LZSK=0.1
LZPK=0.01
PFREE=0.1
SIDE=0.0
RSERV=0.3
UZTWC=37.5
UZFWC=12.5
ADIMC=0.05
LZTWC=50.0
LZFSC=30.0
LZFPC=50.0

# ========================================
# 路由方法参数配置
# ========================================

# 线性水库路由参数配置
[LRParamSet Mengjiang_LR]
GAUGE=Taiping
COEM=1000.0
RIVER=200.0
UNDER=1.0
LEAKO=0.5
LEAKI=0.1
TH=5.0
ISO=0.001
ISU=0.001

# 运动波路由参数配置 (修复NaN问题)
[KWParamSet Mengjiang_KW]
GAUGE=Taiping
UNDER=1.5
LEAKI=0.1
TH=3.0
ISU=0.1
ALPHA=2.5
BETA=0.8
ALPHA0=1.5

# ========================================
# DREAM校准参数配置
# ========================================

# CRESTPHYS模型DREAM校准参数
[CRESTPhysCaliParams CRESTPHYS_CALI]
GAUGE=Taiping
OBJECTIVE=NSCE
DREAM_NDRAW=5000
# 参数格式: 最小值,最大值,初始值
WM=50.0,300.0,150.0
B=1.0,5.0,2.0
IM=0.01,1.0,0.1
KE=0.1,2.0,1.0
FC=1.0,50.0,10.0
KSOIL=0.01,0.5,0.1
IWU=10.0,90.0,50.0
IGW=10.0,90.0,25.0
HMAXAQ=0.5,5.0,1.0
GWC=0.5,2.0,1.0
GWE=0.5,2.0,1.0

# CREST模型DREAM校准参数
[CRESTCaliParams CREST_CALI]
GAUGE=Taiping
OBJECTIVE=NSCE
DREAM_NDRAW=5000
WM=50.0,300.0,150.0
B=1.0,5.0,2.0
IM=0.01,1.0,0.1
KE=0.1,2.0,1.0
FC=1.0,50.0,10.0
IWU=10.0,90.0,50.0

# KW路由DREAM校准参数
[KWCaliParams KW_CALI]
GAUGE=Taiping
UNDER=0.5,10.0,2.0
LEAKI=0.001,0.2,0.05
TH=1.0,15.0,5.0
ISU=0.0,1.0,0.0
ALPHA=1.0,10.0,3.0
BETA=0.5,1.5,0.9
ALPHA0=0.5,10.0,2.0

# LR路由DREAM校准参数
[LRCaliParams LR_CALI]
GAUGE=Taiping
COEM=500.0,2000.0,1000.0
RIVER=50.0,500.0,200.0
UNDER=0.5,5.0,1.0
LEAKO=0.1,1.0,0.5
LEAKI=0.01,0.5,0.1
TH=1.0,15.0,5.0
ISO=0.0001,0.01,0.001
ISU=0.0001,0.01,0.001

# ========================================
# 任务配置 - 多模型多路由组合
# ========================================

# HP + LR 组合任务
[Task HP_LR_Simu]
STYLE=SIMU
MODEL=HP
ROUTING=LR
BASIN=Mengjiang
PRECIP=RAIN_3H
PET=PET_MONTHLY
OUTPUT=output\hp_lr\
PARAM_SET=Mengjiang_HP
ROUTING_PARAM_SET=Mengjiang_LR
TIMESTEP=3h
TIME_BEGIN=************
TIME_END=************

# HP + KW 组合任务
[Task HP_KW_Simu]
STYLE=SIMU
MODEL=HP
ROUTING=KW
BASIN=Mengjiang
PRECIP=RAIN_3H
PET=PET_MONTHLY
OUTPUT=output\hp_kw\
PARAM_SET=Mengjiang_HP
ROUTING_PARAM_SET=Mengjiang_KW
TIMESTEP=3h
TIME_BEGIN=************
TIME_END=************

# CREST + LR 组合任务
[Task CREST_LR_Simu]
STYLE=SIMU
MODEL=CREST
ROUTING=LR
BASIN=Mengjiang
PRECIP=RAIN_3H
PET=PET_MONTHLY
OUTPUT=output\crest_lr\
PARAM_SET=Mengjiang_CREST
ROUTING_PARAM_SET=Mengjiang_LR
TIMESTEP=3h
TIME_BEGIN=************
TIME_END=************

# CREST + KW 组合任务
[Task CREST_KW_Simu]
STYLE=SIMU
MODEL=CREST
ROUTING=KW
BASIN=Mengjiang
PRECIP=RAIN_3H
PET=PET_MONTHLY
OUTPUT=output\crest_kw\
PARAM_SET=Mengjiang_CREST
ROUTING_PARAM_SET=Mengjiang_KW
TIMESTEP=3h
TIME_BEGIN=************
TIME_END=************

# CRESTPHYS + LR 组合任务
[Task CRESTPHYS_LR_Simu]
STYLE=SIMU
MODEL=CRESTPHYS
ROUTING=LR
BASIN=Mengjiang
PRECIP=RAIN_3H
PET=PET_MONTHLY
OUTPUT=output\crestphys_lr\
PARAM_SET=Mengjiang_CRESTPHYS
ROUTING_PARAM_SET=Mengjiang_LR
TIMESTEP=3h
TIME_BEGIN=************
TIME_END=************

# CRESTPHYS + KW 组合任务
[Task CRESTPHYS_KW_Simu]
STYLE=SIMU
MODEL=CRESTPHYS
ROUTING=KW
BASIN=Mengjiang
PRECIP=RAIN_3H
PET=PET_MONTHLY
OUTPUT=output\crestphys_kw\
PARAM_SET=Mengjiang_CRESTPHYS
ROUTING_PARAM_SET=Mengjiang_KW
TIMESTEP=3h
TIME_BEGIN=************
TIME_END=************

# HYMOD + LR 组合任务
[Task HYMOD_LR_Simu]
STYLE=SIMU
MODEL=HYMOD
ROUTING=LR
BASIN=Mengjiang
PRECIP=RAIN_3H
PET=PET_MONTHLY
OUTPUT=output\hymod_lr\
PARAM_SET=Mengjiang_HYMOD
ROUTING_PARAM_SET=Mengjiang_LR
TIMESTEP=3h
TIME_BEGIN=************
TIME_END=************

# HYMOD + KW 组合任务
[Task HYMOD_KW_Simu]
STYLE=SIMU
MODEL=HYMOD
ROUTING=KW
BASIN=Mengjiang
PRECIP=RAIN_3H
PET=PET_MONTHLY
OUTPUT=output\hymod_kw\
PARAM_SET=Mengjiang_HYMOD
ROUTING_PARAM_SET=Mengjiang_KW
TIMESTEP=3h
TIME_BEGIN=************
TIME_END=************

# SAC + LR 组合任务
[Task SAC_LR_Simu]
STYLE=SIMU
MODEL=SAC
ROUTING=LR
BASIN=Mengjiang
PRECIP=RAIN_3H
PET=PET_MONTHLY
OUTPUT=output\sac_lr\
PARAM_SET=Mengjiang_SAC
ROUTING_PARAM_SET=Mengjiang_LR
TIMESTEP=3h
TIME_BEGIN=************
TIME_END=************

# SAC + KW 组合任务
[Task SAC_KW_Simu]
STYLE=SIMU
MODEL=SAC
ROUTING=KW
BASIN=Mengjiang
PRECIP=RAIN_3H
PET=PET_MONTHLY
OUTPUT=output\sac_kw\
PARAM_SET=Mengjiang_SAC
ROUTING_PARAM_SET=Mengjiang_KW
TIMESTEP=3h
TIME_BEGIN=************
TIME_END=************

# ========================================
# DREAM校准任务配置
# ========================================

# CRESTPHYS + KW DREAM校准任务
[Task CRESTPHYS_KW_Calibration]
STYLE=CALI_DREAM
MODEL=CRESTPHYS
ROUTING=KW
BASIN=Mengjiang
PRECIP=RAIN_3H
PET=PET_MONTHLY
OUTPUT=output\calibration\crestphys_kw\
PARAM_SET=Mengjiang_CRESTPHYS
ROUTING_PARAM_SET=Mengjiang_KW
CALI_PARAM=CRESTPHYS_CALI
ROUTING_CALI_PARAM=KW_CALI
TIMESTEP=3h
TIME_BEGIN=************
TIME_WARMEND=************
TIME_END=************

# CREST + KW DREAM校准任务
[Task CREST_KW_Calibration]
STYLE=CALI_DREAM
MODEL=CREST
ROUTING=KW
BASIN=Mengjiang
PRECIP=RAIN_3H
PET=PET_MONTHLY
OUTPUT=output\calibration\crest_kw\
PARAM_SET=Mengjiang_CREST
ROUTING_PARAM_SET=Mengjiang_KW
CALI_PARAM=CREST_CALI
ROUTING_CALI_PARAM=KW_CALI
TIMESTEP=3h
TIME_BEGIN=***********
TIME_WARMEND=***********
TIME_END=***********

# CRESTPHYS + LR DREAM校准任务
[Task CRESTPHYS_LR_Calibration]
STYLE=CALI_DREAM
MODEL=CRESTPHYS
ROUTING=LR
BASIN=Mengjiang
PRECIP=RAIN_3H
PET=PET_MONTHLY
OUTPUT=output\calibration\crestphys_lr\
PARAM_SET=Mengjiang_CRESTPHYS
ROUTING_PARAM_SET=Mengjiang_LR
CALI_PARAM=CRESTPHYS_CALI
ROUTING_CALI_PARAM=LR_CALI
TIMESTEP=3h
TIME_BEGIN=************
TIME_WARMEND=************
TIME_END=************

# ========================================
# 执行配置
# ========================================

[Execute]
# 选择要执行的任务 - 取消注释您想要运行的任务

# === 模拟运行任务 ===
# TASK=HP_LR_Simu
# TASK=HP_KW_Simu
# TASK=CREST_LR_Simu
# TASK=CREST_KW_Simu
# TASK=CRESTPHYS_LR_Simu
TASK=CRESTPHYS_KW_Simu
# TASK=HYMOD_LR_Simu
# TASK=HYMOD_KW_Simu
# TASK=SAC_LR_Simu
# TASK=SAC_KW_Simu

# === DREAM校准任务 ===
# TASK=CRESTPHYS_KW_Calibration
# TASK=CREST_KW_Calibration
# TASK=CRESTPHYS_LR_Calibration
